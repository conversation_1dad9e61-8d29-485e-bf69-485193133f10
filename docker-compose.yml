---
# Simple Traefik Configuration for API with WebSocket support
# Uses Cloudflare DNS for HTTPS certificates

services:
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API Port
      - "8202:8080"   # Dashboard
    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro

      # Traefik configuration
      - ./config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/

      # Use existing Let's Encrypt certificates directly (no ACME setup needed)
      - /etc/letsencrypt/live/eko-api2.nextai.asia/:/etc/letsencrypt/live/eko-api2.nextai.asia/:ro

      # ACME storage - COMMENTED OUT to avoid rate limits and certificate creation
      # - ./config/certs/:/etc/traefik/certs/
      # - ./config/certs/cloudflare-acme.json:/etc/traefik/certs/cloudflare-acme.json
    environment:
      - DOMAIN=${DOMAIN:-localhost}
    # Simple startup command - using existing certificates directly
    command:
      - /bin/sh
      - -c
      - |
        echo "🌐 DOMAIN: $$DOMAIN"
        echo "� Using existing Let's Encrypt certificates from /etc/letsencrypt/live/eko-api2.nextai.asia/"
        echo "✅ No ACME setup needed - avoiding rate limits completely"
        traefik --configfile=/etc/traefik/traefik.yaml

    # ACME-related environment variables - COMMENTED OUT since we're using existing certs
    # - CF_DNS_API_TOKEN=${CF_DNS_API_TOKEN}
    # - CF_EMAIL=${CF_EMAIL}

  # Your API Application (WebSocket enabled)
  eko-api:
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
    extra_hosts:
      - "minio.nextai.asia:*************"
    labels:
      - "traefik.enable=true"

      # Main API routes (HTTPS)
      - "traefik.http.routers.api.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api.entrypoints=websecure"
      # Using existing certificates from cloudflare-acme.json
      - "traefik.http.routers.api.tls.certresolver=cloudflare"
      - "traefik.http.routers.api.middlewares=default-headers@file"

      # HTTP API on port 8201
      - "traefik.http.routers.api-http.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api-http.entrypoints=http-api"
      - "traefik.http.routers.api-http.middlewares=default-headers@file"

      # Service configuration with WebSocket sticky sessions
      - "traefik.http.services.eko-api.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-api.loadbalancer.sticky.cookie.name=eko-session"


