# Christian Lempa Style Traefik Configuration
# Clean, simple, and effective for API with WebSocket support

global:
  checkNewVersion: false
  sendAnonymousUsage: false

# Logging
accessLog: {}
log:
  level: INFO

# API and Dashboard
api:
  dashboard: true
  insecure: true
  debug: false

# Entry Points
entryPoints:
  # HTTP entry point (redirects to HTTPS)
  web:
    address: :80
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https

  # HTTPS entry point
  websecure:
    address: :443

  # HTTP API entry point (port 8201)
  http-api:
    address: :8201

# Certificate Resolvers (Cloudflare DNS) - COMMENTED OUT to avoid rate limits
# certificatesResolvers:
#   cloudflare:
#     acme:
#       email: "${CF_EMAIL}"
#       storage: /etc/traefik/certs/cloudflare-acme.json
#       caServer: 'https://acme-v02.api.letsencrypt.org/directory'
#       keyType: EC256
#       dnsChallenge:
#         provider: cloudflare
#         resolvers:
#           - "*******:53"
#           - "*******:53"

# Use existing Let's Encrypt certificates directly (no ACME, no rate limits)
tls:
  certificates:
    - certFile: /etc/letsencrypt/live/eko-api2.nextai.asia/fullchain.pem
      keyFile: /etc/letsencrypt/live/eko-api2.nextai.asia/privkey.pem

# Server Transport (for backend connections)
serversTransport:
  insecureSkipVerify: true

# Providers
providers:
  # Docker provider for automatic service discovery
  docker:
    exposedByDefault: false
    endpoint: 'unix:///var/run/docker.sock'
    watch: true

  # File provider for static configurations
  file:
    directory: /etc/traefik/conf/
    watch: true
