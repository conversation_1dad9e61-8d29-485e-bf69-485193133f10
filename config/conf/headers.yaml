# Headers and Middleware Configuration
# Optimized for API with WebSocket support

http:
  middlewares:
    # Default headers for all requests
    default-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          # Security headers
          Strict-Transport-Security: "max-age=31536000; includeSubDomains"
          X-Frame-Options: "DENY"
          X-Content-Type-Options: "nosniff"
          X-XSS-Protection: "1; mode=block"
          Referrer-Policy: "strict-origin-when-cross-origin"
          
          # CORS headers for API
          Access-Control-Allow-Origin: "*"
          Access-Control-Allow-Methods: "GET,POST,PUT,DELETE,OPTIONS"
          Access-Control-Allow-Headers: "*"
          Access-Control-Max-Age: "86400"

    # WebSocket specific headers
    websocket-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          # WebSocket CORS
          Access-Control-Allow-Origin: "*"
          Access-Control-Allow-Methods: "GET,POST,OPTIONS"
          Access-Control-Allow-Headers: "*"
          Access-Control-Allow-Credentials: "true"

    # Rate limiting for API
    rate-limit:
      rateLimit:
        burst: 100
        average: 50
        period: "1m"

    # Compression
    compression:
      compress:
        excludedContentTypes:
          - "text/event-stream"
          - "application/grpc"

    # Security headers for sensitive endpoints
    secure-headers:
      headers:
        customResponseHeaders:
          Strict-Transport-Security: "max-age=31536000; includeSubDomains; preload"
          X-Frame-Options: "DENY"
          X-Content-Type-Options: "nosniff"
          Content-Security-Policy: "default-src 'self'; connect-src 'self' wss: ws:;"
